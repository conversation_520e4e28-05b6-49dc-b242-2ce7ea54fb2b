import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:invoice_automate/pages/voice_record_page.dart';

void main() {
  group('iOS Compatibility Tests', () {
    testWidgets('VoiceRecordPage should handle speech recognition unavailable gracefully', (WidgetTester tester) async {
      // 构建VoiceRecordPage
      await tester.pumpWidget(
        MaterialApp(
          home: const VoiceRecordPage(),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('语音录制'), findsOneWidget);

      // 在测试环境中，语音识别通常不可用
      // 验证是否显示了适当的错误信息或替代方案
      final speechUnavailable = find.textContaining('语音识别不可用');
      final recordingInstructions = find.text('按住下方按钮开始录音\n上滑取消录制');

      expect(
        speechUnavailable.evaluate().isNotEmpty || recordingInstructions.evaluate().isNotEmpty,
        isTrue,
      );
    });

    testWidgets('VoiceRecordPage should show text input option when speech is unavailable', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const VoiceRecordPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 如果语音识别不可用，应该显示文本输入按钮
      final textInputButton = find.text('使用文本输入测试');
      if (textInputButton.evaluate().isNotEmpty) {
        // 点击文本输入按钮
        await tester.tap(textInputButton);
        await tester.pumpAndSettle();

        // 验证文本输入对话框出现
        expect(find.text('文本输入模式'), findsOneWidget);
        expect(find.text('请输入交易信息（模拟语音输入）：'), findsOneWidget);
        
        // 验证输入框存在
        expect(find.byType(TextField), findsOneWidget);
        
        // 验证按钮存在
        expect(find.text('取消'), findsOneWidget);
        expect(find.text('确认'), findsOneWidget);
      }
    });

    testWidgets('Text input dialog should work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const VoiceRecordPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 查找文本输入按钮
      final textInputButton = find.text('使用文本输入测试');
      if (textInputButton.evaluate().isNotEmpty) {
        // 点击文本输入按钮
        await tester.tap(textInputButton);
        await tester.pumpAndSettle();

        // 在输入框中输入测试文本
        const testText = '我在星巴克花了35块钱买咖啡';
        await tester.enterText(find.byType(TextField), testText);
        await tester.pumpAndSettle();

        // 验证文本已输入
        expect(find.text(testText), findsOneWidget);

        // 点击取消按钮应该关闭对话框
        await tester.tap(find.text('取消'));
        await tester.pumpAndSettle();

        // 验证对话框已关闭
        expect(find.text('文本输入模式'), findsNothing);
      }
    });

    testWidgets('VoiceRecordPage should handle initialization errors gracefully', (WidgetTester tester) async {
      // 这个测试验证页面在语音识别初始化失败时不会崩溃
      await tester.pumpWidget(
        MaterialApp(
          home: const VoiceRecordPage(),
        ),
      );

      // 等待初始化完成
      await tester.pumpAndSettle();

      // 页面应该正常显示，不应该崩溃
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
      
      // 应该有某种形式的用户界面（录音按钮或错误信息）
      final gestureDetector = find.byType(GestureDetector);
      final speechText = find.textContaining('语音识别');

      expect(
        gestureDetector.evaluate().isNotEmpty || speechText.evaluate().isNotEmpty,
        isTrue,
      );
    });

    testWidgets('VoiceRecordPage should dispose resources properly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const VoiceRecordPage(),
        ),
      );

      await tester.pumpAndSettle();

      // 导航到其他页面以触发dispose
      await tester.pumpWidget(
        MaterialApp(
          home: const Scaffold(
            body: Text('Other Page'),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 如果没有抛出异常，说明资源清理正常
      expect(find.text('Other Page'), findsOneWidget);
    });
  });
}
