import 'package:flutter_test/flutter_test.dart';
import 'package:invoice_automate/models/voice_transaction_data.dart';
import 'package:invoice_automate/services/gemini_service.dart';

void main() {
  group('Voice Recognition Tests', () {
    test('VoiceTransactionData should parse JSON correctly', () {
      final json = {
        'merchant_name': '星巴克',
        'amount': 35.5,
        'transaction_time': '2024-01-15T10:30:00.000Z',
        'description': '购买咖啡',
        'category': '餐饮',
        'payment_method': '支付宝',
        'location': '北京朝阳区',
      };

      final voiceData = VoiceTransactionData.fromJson(json);

      expect(voiceData.merchantName, equals('星巴克'));
      expect(voiceData.amount, equals(35.5));
      expect(voiceData.description, equals('购买咖啡'));
      expect(voiceData.category, equals('餐饮'));
      expect(voiceData.paymentMethod, equals('支付宝'));
      expect(voiceData.location, equals('北京朝阳区'));
      expect(voiceData.transactionTime, isNotNull);
      expect(voiceData.isValid, isTrue);
    });

    test('VoiceTransactionData should handle missing fields', () {
      final json = {
        'merchant_name': '测试商家',
        'amount': 100.0,
      };

      final voiceData = VoiceTransactionData.fromJson(json);

      expect(voiceData.merchantName, equals('测试商家'));
      expect(voiceData.amount, equals(100.0));
      expect(voiceData.description, isNull);
      expect(voiceData.category, isNull);
      expect(voiceData.paymentMethod, isNull);
      expect(voiceData.location, isNull);
      expect(voiceData.isValid, isTrue);
    });

    test('VoiceTransactionData should validate correctly', () {
      // Valid data
      final validData = VoiceTransactionData(
        merchantName: '测试商家',
        amount: 50.0,
      );
      expect(validData.isValid, isTrue);

      // Invalid data - no merchant name
      final invalidData1 = VoiceTransactionData(
        amount: 50.0,
      );
      expect(invalidData1.isValid, isFalse);

      // Invalid data - empty merchant name
      final invalidData2 = VoiceTransactionData(
        merchantName: '',
        amount: 50.0,
      );
      expect(invalidData2.isValid, isFalse);

      // Invalid data - zero amount
      final invalidData3 = VoiceTransactionData(
        merchantName: '测试商家',
        amount: 0.0,
      );
      expect(invalidData3.isValid, isFalse);

      // Invalid data - negative amount
      final invalidData4 = VoiceTransactionData(
        merchantName: '测试商家',
        amount: -10.0,
      );
      expect(invalidData4.isValid, isFalse);
    });

    test('VoiceTransactionData should handle invalid date parsing', () {
      final json = {
        'merchant_name': '测试商家',
        'amount': 100.0,
        'transaction_time': 'invalid-date',
      };

      final voiceData = VoiceTransactionData.fromJson(json);

      expect(voiceData.merchantName, equals('测试商家'));
      expect(voiceData.amount, equals(100.0));
      expect(voiceData.transactionTime, isNotNull); // 应该使用当前时间
    });

    test('VoiceTransactionData copyWith should work correctly', () {
      final original = VoiceTransactionData(
        merchantName: '原始商家',
        amount: 100.0,
        description: '原始描述',
      );

      final updated = original.copyWith(
        merchantName: '更新商家',
        amount: 200.0,
      );

      expect(updated.merchantName, equals('更新商家'));
      expect(updated.amount, equals(200.0));
      expect(updated.description, equals('原始描述')); // 未更新的字段保持不变
    });

    test('VoiceTransactionData toJson should work correctly', () {
      final data = VoiceTransactionData(
        merchantName: '测试商家',
        amount: 100.0,
        transactionTime: DateTime(2024, 1, 15, 10, 30),
        description: '测试描述',
        category: '测试类别',
        paymentMethod: '现金',
        location: '测试地点',
      );

      final json = data.toJson();

      expect(json['merchant_name'], equals('测试商家'));
      expect(json['amount'], equals(100.0));
      expect(json['transaction_time'], equals('2024-01-15T10:30:00.000'));
      expect(json['description'], equals('测试描述'));
      expect(json['category'], equals('测试类别'));
      expect(json['payment_method'], equals('现金'));
      expect(json['location'], equals('测试地点'));
    });
  });
}
