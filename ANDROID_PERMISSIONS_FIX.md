# Android语音识别权限修复指南

## 问题描述
在Android设备上，语音识别功能显示"Speech recognition not available"，无法正常工作。

## 根本原因
Android应用需要在AndroidManifest.xml中声明特定的权限，并在运行时请求用户授权。

## 修复步骤

### 1. 添加AndroidManifest.xml权限
在 `android/app/src/main/AndroidManifest.xml` 中添加了以下权限：

```xml
<!-- Permissions for voice recognition -->
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
```

### 2. 添加Android SDK 30+支持
在queries部分添加了语音识别服务查询：

```xml
<queries>
    <!-- 现有的PROCESS_TEXT查询 -->
    <intent>
        <action android:name="android.intent.action.PROCESS_TEXT"/>
        <data android:mimeType="text/plain"/>
    </intent>
    <!-- 新增：语音识别服务查询 -->
    <intent>
        <action android:name="android.speech.RecognitionService" />
    </intent>
</queries>
```

### 3. 添加运行时权限请求
在 `VoiceRecordPage` 中添加了：
- 权限检查方法 `_checkPermissions()`
- 权限请求对话框 `_showPermissionDialog()`
- 自动权限检查和重试功能

### 4. 改进用户界面
- 添加"重新检查"按钮
- 提供更清晰的错误信息
- 支持打开系统设置页面

## 权限说明

### 必需权限：
1. **RECORD_AUDIO**: 录制音频的基本权限
2. **BLUETOOTH**: 支持蓝牙耳机录音
3. **BLUETOOTH_ADMIN**: 蓝牙设备管理
4. **BLUETOOTH_CONNECT**: Android 12+蓝牙连接权限

### 自动包含的权限：
- **INTERNET**: speech_to_text包自动添加（网络语音识别）

## 使用流程

### 首次使用：
1. 用户点击语音按钮
2. 应用检查麦克风权限
3. 如果未授权，自动弹出权限请求
4. 用户授权后即可使用语音功能

### 权限被拒绝：
1. 显示权限说明对话框
2. 提供"打开设置"按钮
3. 用户可手动在设置中授权

### 权限被永久拒绝：
1. 显示设置引导对话框
2. 自动跳转到应用设置页面
3. 用户需要手动开启麦克风权限

## 故障排除

### 常见问题：

1. **权限已授予但仍不可用**：
   - 检查设备是否支持语音识别
   - 确认Google应用已安装并启用
   - 重启应用重新初始化

2. **Android模拟器问题**：
   - 某些模拟器不支持语音识别
   - 使用文本输入功能进行测试
   - 建议在真实设备上测试

3. **特定设备问题**：
   - 某些定制Android系统可能有限制
   - 检查设备的语音识别设置
   - 确认Google服务可用

### 调试步骤：

1. **检查权限状态**：
   ```dart
   final status = await Permission.microphone.status;
   print('Microphone permission: $status');
   ```

2. **检查语音识别可用性**：
   ```dart
   final available = await speech.initialize();
   print('Speech recognition available: $available');
   ```

3. **查看系统日志**：
   - 使用 `flutter logs` 查看详细错误信息
   - 关注 "Speech recognition" 相关日志

## 测试建议

### 开发阶段：
- 在多种Android设备上测试
- 测试权限拒绝和授权场景
- 验证蓝牙耳机兼容性

### 发布前：
- 在不同Android版本上测试
- 验证权限请求流程
- 测试网络环境下的语音识别

## 代码变更总结

### 修改的文件：
1. `android/app/src/main/AndroidManifest.xml` - 添加权限声明
2. `lib/pages/voice_record_page.dart` - 添加权限检查和UI改进

### 新增功能：
- ✅ 运行时权限请求
- ✅ 权限状态检查
- ✅ 设置页面跳转
- ✅ 用户友好的错误提示
- ✅ 重新检查功能

## 注意事项

1. **权限最佳实践**：
   - 在需要时才请求权限
   - 提供清晰的权限说明
   - 优雅处理权限拒绝

2. **用户体验**：
   - 提供替代方案（文本输入）
   - 清晰的错误信息和解决方案
   - 避免重复弹出权限请求

3. **兼容性**：
   - 支持Android 6.0+的运行时权限
   - 兼容Android 12+的新权限模型
   - 处理不同厂商的定制系统

现在Android设备应该能够正常请求麦克风权限并使用语音识别功能了！
