# 语音识别功能使用指南

## 功能概述

本应用新增了语音识别功能，用户可以通过语音输入快速创建交易记录。该功能使用Google Gemini AI来分析语音内容并提取交易信息。

## 主要特性

### 1. 语音录制
- **按住录音**: 按住主页底部的语音按钮开始录音
- **上滑取消**: 录音过程中向上滑动可以取消录制
- **时长检测**: 自动检测录音时长，过短的录音会提示错误
- **实时反馈**: 录音过程中显示识别的文本内容和置信度

### 2. 智能识别
- **多语言支持**: 支持中文语音识别
- **信息提取**: 自动从语音中提取以下信息：
  - 商家名称
  - 交易金额
  - 交易时间（如未提及则使用当前时间）
  - 交易描述
  - 交易类别
  - 支付方式
  - 交易地点

### 3. 交易方匹配
- **智能匹配**: 自动搜索系统中已有的交易方
- **模糊搜索**: 支持商家名称的模糊匹配
- **新建选项**: 如果没有匹配的交易方，可以创建新的交易方

### 4. 数据确认与编辑
- **可编辑表单**: 所有识别的信息都可以手动编辑
- **时间选择**: 支持自定义交易时间
- **数据验证**: 保存前验证必填字段

## 使用流程

### 步骤1: 开始录音
1. 在主页点击底部的"Voice"按钮
2. 进入语音录制页面
3. 按住录音按钮开始录音

### 步骤2: 录制语音
1. 清晰地说出交易信息，例如：
   - "我在星巴克花了35块钱买咖啡，用支付宝付的"
   - "今天中午在麦当劳消费28元"
   - "昨天晚上打车花了45块钱"

2. 录音过程中可以看到实时识别的文本
3. 如需取消，向上滑动录音按钮

### 步骤3: 确认录音
1. 松开录音按钮结束录音
2. 系统会显示录音时长、识别内容和置信度
3. 点击"确认提交"继续，或"重新录制"重新开始

### 步骤4: 确认信息
1. 系统会自动分析语音内容并提取交易信息
2. 如果找到匹配的交易方，会显示在下拉列表中
3. 检查并编辑所有字段信息
4. 点击"保存交易记录"完成创建

## 语音输入建议

### 有效的语音输入示例：
- "我在肯德基花了42块钱买午餐"
- "昨天在超市买菜花了68元，用现金付的"
- "今天上午打车去机场，花了120块钱"
- "在咖啡店买了一杯拿铁，35元，支付宝付款"

### 语音输入技巧：
1. **清晰发音**: 确保发音清晰，语速适中
2. **包含关键信息**: 尽量包含商家名称和金额
3. **环境安静**: 在安静的环境中录音效果更好
4. **完整句子**: 使用完整的句子描述交易

## 技术实现

### 核心组件
- **VoiceRecordPage**: 语音录制界面
- **VoiceResultPage**: 结果确认界面
- **VoiceTransactionData**: 语音交易数据模型
- **GeminiService**: AI分析服务

### 数据库集成
- 自动搜索匹配的交易方
- 支持创建新的交易方
- 保存完整的交易记录

### 权限要求
- **iOS**: 麦克风权限、语音识别权限（自动在Info.plist中配置）
- **Android**: 麦克风权限、蓝牙权限（运行时自动请求）
- **网络权限**: 用于AI分析（自动配置）

## 故障排除

### 常见问题

1. **应用崩溃或无法录音**
   - **iOS模拟器**: iOS模拟器不支持语音识别功能，请在真实iOS设备上测试
   - **Android权限**: 首次使用时会自动请求麦克风权限，请点击"允许"
   - **设备兼容性**: 确保设备支持语音识别功能

2. **权限问题**
   - **Android**: 如果权限被拒绝，点击"重新检查"或"打开设置"手动授权
   - **iOS**: 在设置 > 隐私与安全 > 麦克风中允许应用访问
   - 权限授权后可能需要重启应用

3. **模拟器测试**
   - **iOS模拟器**: 显示"语音识别不可用"，使用"文本输入测试"功能
   - **Android模拟器**: 某些模拟器不支持语音识别，建议在真实设备测试
   - 文本输入功能与语音识别具有相同的AI分析能力

3. **识别不准确**
   - 在安静环境中重新录音
   - 说话清晰，语速适中
   - 使用简单明确的表达

4. **无法保存**
   - 检查网络连接
   - 确保Gemini API密钥配置正确
   - 验证必填字段是否完整

5. **找不到匹配的交易方**
   - 系统会自动创建新的交易方
   - 可以手动编辑商家名称

### 错误信息说明
- "语音识别不可用": 设备不支持语音识别或权限未授予
- "录音时间过短": 录音时长少于1秒
- "未识别到语音内容": 语音识别失败
- "分析语音内容时出错": AI分析失败
- "保存失败": 数据库操作失败

### iOS模拟器注意事项
- iOS模拟器不支持麦克风和语音识别功能
- 应用会自动检测并提供文本输入替代方案
- 建议在真实iOS设备上测试完整的语音功能
- 文本输入模式可以用于开发和测试AI分析功能

## 更新日志

### v1.0.0
- 新增语音录制功能
- 集成Gemini AI分析
- 支持交易方智能匹配
- 添加数据确认和编辑界面
- 修复iOS底部安全区域问题
