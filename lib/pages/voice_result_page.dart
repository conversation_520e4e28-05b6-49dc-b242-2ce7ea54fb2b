import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/gemini_service.dart';
import '../models/voice_transaction_data.dart';
import '../models/transaction.dart';
import '../models/party.dart';
import '../services/party_service.dart';
import '../services/transaction_service.dart';

class VoiceResultPage extends StatefulWidget {
  final String recognizedText;
  final double confidence;
  final int recordingDuration;

  const VoiceResultPage({
    super.key,
    required this.recognizedText,
    required this.confidence,
    required this.recordingDuration,
  });

  @override
  State<VoiceResultPage> createState() => _VoiceResultPageState();
}

class _VoiceResultPageState extends State<VoiceResultPage> {
  final _formKey = GlobalKey<FormState>();
  final _geminiService = GeminiService();
  final _partyService = PartyService();
  final _transactionService = TransactionService();
  
  // 表单控制器
  final _merchantNameController = TextEditingController();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _categoryController = TextEditingController();
  final _paymentMethodController = TextEditingController();
  final _locationController = TextEditingController();
  
  DateTime _selectedDateTime = DateTime.now();
  // VoiceTransactionData? _extractedData; // 暂时注释掉未使用的字段
  bool _isLoading = true;
  bool _isSaving = false;
  String? _errorMessage;
  
  // 匹配的交易方列表
  List<Party> _matchedParties = [];
  Party? _selectedParty;

  @override
  void initState() {
    super.initState();
    _analyzeVoiceText();
  }

  Future<void> _analyzeVoiceText() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final result = await _geminiService.analyzeVoiceText(widget.recognizedText);
      
      if (result != null) {
        setState(() {
          // _extractedData = result; // 暂时注释掉
          _populateForm(result);
        });
        
        // 尝试匹配交易方
        if (result.merchantName != null && result.merchantName!.isNotEmpty) {
          await _searchMatchingParties(result.merchantName!);
        }
      } else {
        setState(() {
          _errorMessage = '无法解析语音内容，请手动填写信息';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = '分析语音内容时出错: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _populateForm(VoiceTransactionData data) {
    _merchantNameController.text = data.merchantName ?? '';
    _amountController.text = data.amount?.toString() ?? '';
    _descriptionController.text = data.description ?? '';
    _categoryController.text = data.category ?? '';
    _paymentMethodController.text = data.paymentMethod ?? '';
    _locationController.text = data.location ?? '';
    
    if (data.transactionTime != null) {
      _selectedDateTime = data.transactionTime!;
    }
  }

  Future<void> _searchMatchingParties(String merchantName) async {
    try {
      final parties = await _partyService.searchByName(merchantName);
      setState(() {
        _matchedParties = parties;
        if (parties.isNotEmpty) {
          _selectedParty = parties.first; // 默认选择第一个匹配的
        }
      });
    } catch (e) {
      print('搜索交易方时出错: $e');
    }
  }

  Future<void> _selectDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDateTime,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_selectedDateTime),
      );
      
      if (time != null) {
        setState(() {
          _selectedDateTime = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  Future<void> _saveTransaction() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      // 如果没有选择现有交易方，创建新的交易方
      Party party;
      if (_selectedParty != null) {
        party = _selectedParty!;
      } else {
        // 创建新的交易方
        party = Party(
          name: _merchantNameController.text.trim(),
          address: _locationController.text.trim().isNotEmpty
              ? _locationController.text.trim()
              : null,
          type: PartyType.supplier, // 默认为供应商
        );
        party = await _partyService.create(party);
      }

      // 创建交易记录
      final transaction = Transaction(
        partyId: party.id,
        amount: double.parse(_amountController.text),
        transactionTime: _selectedDateTime,
        description: _buildTransactionDescription(),
      );

      await _transactionService.create(transaction);

      // 检查widget是否仍然mounted
      if (!mounted) return;

      // 显示成功消息并返回
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('交易记录保存成功！'),
          backgroundColor: Colors.green,
        ),
      );

      Navigator.of(context).popUntil((route) => route.isFirst);
    } catch (e) {
      // 检查widget是否仍然mounted
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('保存失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  String _buildTransactionDescription() {
    final parts = <String>[];
    
    if (_descriptionController.text.isNotEmpty) {
      parts.add(_descriptionController.text);
    }
    
    if (_categoryController.text.isNotEmpty) {
      parts.add('类别: ${_categoryController.text}');
    }
    
    if (_paymentMethodController.text.isNotEmpty) {
      parts.add('支付方式: ${_paymentMethodController.text}');
    }
    
    if (_locationController.text.isNotEmpty) {
      parts.add('地点: ${_locationController.text}');
    }
    
    return parts.join(' | ');
  }

  @override
  void dispose() {
    _merchantNameController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    _categoryController.dispose();
    _paymentMethodController.dispose();
    _locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('确认交易信息'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (!_isLoading && !_isSaving)
            TextButton(
              onPressed: _saveTransaction,
              child: const Text('保存', style: TextStyle(color: Colors.white)),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 原始语音信息卡片
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '原始语音信息',
                              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            Text('录音时长: ${widget.recordingDuration}秒'),
                            Text('置信度: ${(widget.confidence * 100).toStringAsFixed(1)}%'),
                            const SizedBox(height: 8),
                            Text('识别内容: ${widget.recognizedText}'),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    if (_errorMessage != null)
                      Card(
                        color: Colors.red[50],
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            _errorMessage!,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                      ),
                    
                    const SizedBox(height: 16),
                    
                    // 交易方选择
                    if (_matchedParties.isNotEmpty) ...[
                      const Text(
                        '匹配的交易方',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<Party>(
                        value: _selectedParty,
                        decoration: const InputDecoration(
                          labelText: '选择交易方',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          const DropdownMenuItem<Party>(
                            value: null,
                            child: Text('创建新交易方'),
                          ),
                          ..._matchedParties.map((party) => DropdownMenuItem<Party>(
                            value: party,
                            child: Text(party.name),
                          )),
                        ],
                        onChanged: (Party? value) {
                          setState(() {
                            _selectedParty = value;
                          });
                        },
                      ),
                      const SizedBox(height: 16),
                    ],
                    
                    // 表单字段
                    TextFormField(
                      controller: _merchantNameController,
                      decoration: const InputDecoration(
                        labelText: '商家名称 *',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return '请输入商家名称';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _amountController,
                      decoration: const InputDecoration(
                        labelText: '金额 *',
                        border: OutlineInputBorder(),
                        prefixText: '¥ ',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return '请输入金额';
                        }
                        final amount = double.tryParse(value);
                        if (amount == null || amount <= 0) {
                          return '请输入有效的金额';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // 交易时间选择
                    InkWell(
                      onTap: _selectDateTime,
                      child: InputDecorator(
                        decoration: const InputDecoration(
                          labelText: '交易时间',
                          border: OutlineInputBorder(),
                        ),
                        child: Text(
                          '${_selectedDateTime.year}-${_selectedDateTime.month.toString().padLeft(2, '0')}-${_selectedDateTime.day.toString().padLeft(2, '0')} '
                          '${_selectedDateTime.hour.toString().padLeft(2, '0')}:${_selectedDateTime.minute.toString().padLeft(2, '0')}',
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: '交易描述',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 2,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _categoryController,
                      decoration: const InputDecoration(
                        labelText: '交易类别',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _paymentMethodController,
                      decoration: const InputDecoration(
                        labelText: '支付方式',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    TextFormField(
                      controller: _locationController,
                      decoration: const InputDecoration(
                        labelText: '交易地点',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // 保存按钮
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSaving ? null : _saveTransaction,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: _isSaving
                            ? const CircularProgressIndicator()
                            : const Text('保存交易记录', style: TextStyle(fontSize: 16)),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
