/// 语音识别交易数据模型
class VoiceTransactionData {
  final String? merchantName; // 商家名称
  final double? amount; // 交易金额
  final DateTime? transactionTime; // 交易时间
  final String? description; // 交易描述
  final String? category; // 交易类别
  final String? paymentMethod; // 支付方式
  final String? location; // 交易地点

  VoiceTransactionData({
    this.merchantName,
    this.amount,
    this.transactionTime,
    this.description,
    this.category,
    this.paymentMethod,
    this.location,
  });

  /// 从JSON创建VoiceTransactionData对象
  factory VoiceTransactionData.fromJson(Map<String, dynamic> json) {
    DateTime? parsedTime;
    if (json['transaction_time'] != null) {
      try {
        parsedTime = DateTime.parse(json['transaction_time'] as String);
      } catch (e) {
        // 如果解析失败，使用当前时间
        parsedTime = DateTime.now();
      }
    }

    return VoiceTransactionData(
      merchantName: json['merchant_name'] as String?,
      amount: json['amount'] != null ? (json['amount'] as num).toDouble() : null,
      transactionTime: parsedTime,
      description: json['description'] as String?,
      category: json['category'] as String?,
      paymentMethod: json['payment_method'] as String?,
      location: json['location'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'merchant_name': merchantName,
      'amount': amount,
      'transaction_time': transactionTime?.toIso8601String(),
      'description': description,
      'category': category,
      'payment_method': paymentMethod,
      'location': location,
    };
  }

  /// 创建副本并更新指定字段
  VoiceTransactionData copyWith({
    String? merchantName,
    double? amount,
    DateTime? transactionTime,
    String? description,
    String? category,
    String? paymentMethod,
    String? location,
  }) {
    return VoiceTransactionData(
      merchantName: merchantName ?? this.merchantName,
      amount: amount ?? this.amount,
      transactionTime: transactionTime ?? this.transactionTime,
      description: description ?? this.description,
      category: category ?? this.category,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      location: location ?? this.location,
    );
  }

  /// 检查是否有足够的信息创建交易记录
  bool get isValid {
    return merchantName != null && 
           merchantName!.isNotEmpty && 
           amount != null && 
           amount! > 0;
  }

  @override
  String toString() {
    return 'VoiceTransactionData{merchantName: $merchantName, amount: $amount, transactionTime: $transactionTime, description: $description}';
  }
}
