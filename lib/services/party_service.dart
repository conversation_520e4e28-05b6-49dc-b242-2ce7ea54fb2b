import 'package:sembast/sembast.dart';
import '../models/party.dart';
import 'database_service.dart';

/// 交易方信息数据库服务
class PartyService {
  static final PartyService _instance = PartyService._internal();
  factory PartyService() => _instance;
  PartyService._internal();

  final DatabaseService _databaseService = DatabaseService();
  final StoreRef<String, Map<String, Object?>> _store = 
      stringMapStoreFactory.store('parties');

  /// 创建新的交易方
  Future<Party> create(Party party) async {
    try {
      final db = await _databaseService.database;
      await _store.record(party.id).put(db, party.toMap());
      return party;
    } catch (e) {
      throw Exception('Failed to create party: $e');
    }
  }

  /// 根据ID获取交易方
  Future<Party?> getById(String id) async {
    try {
      final db = await _databaseService.database;
      final map = await _store.record(id).get(db);
      return map != null ? Party.fromMap(map) : null;
    } catch (e) {
      throw Exception('Failed to get party by id: $e');
    }
  }

  /// 获取所有交易方
  Future<List<Party>> getAll() async {
    try {
      final db = await _databaseService.database;
      final records = await _store.find(db);
      return records.map((record) => Party.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to get all parties: $e');
    }
  }

  /// 根据类型获取交易方
  Future<List<Party>> getByType(PartyType type) async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.equals('type', type.toString()),
      );
      final records = await _store.find(db, finder: finder);
      return records.map((record) => Party.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to get parties by type: $e');
    }
  }

  /// 根据名称搜索交易方（支持模糊搜索）
  Future<List<Party>> searchByName(String name) async {
    try {
      final db = await _databaseService.database;

      // 获取所有交易方并进行客户端过滤以支持模糊搜索
      final allRecords = await _store.find(db);
      final searchTerm = name.toLowerCase();

      final matchedRecords = allRecords.where((record) {
        final partyName = (record.value['name'] as String? ?? '').toLowerCase();
        return partyName.contains(searchTerm);
      }).toList();

      return matchedRecords.map((record) => Party.fromMap(record.value)).toList();
    } catch (e) {
      throw Exception('Failed to search parties by name: $e');
    }
  }

  /// 更新交易方信息
  Future<Party> update(Party party) async {
    try {
      final db = await _databaseService.database;
      final updatedParty = party.copyWith();
      await _store.record(party.id).put(db, updatedParty.toMap());
      return updatedParty;
    } catch (e) {
      throw Exception('Failed to update party: $e');
    }
  }

  /// 删除交易方
  Future<bool> delete(String id) async {
    try {
      final db = await _databaseService.database;
      final key = await _store.record(id).delete(db);
      return key != null;
    } catch (e) {
      throw Exception('Failed to delete party: $e');
    }
  }

  /// 检查交易方是否存在
  Future<bool> exists(String id) async {
    try {
      final party = await getById(id);
      return party != null;
    } catch (e) {
      throw Exception('Failed to check if party exists: $e');
    }
  }

  /// 获取交易方总数
  Future<int> count() async {
    try {
      final db = await _databaseService.database;
      return await _store.count(db);
    } catch (e) {
      throw Exception('Failed to count parties: $e');
    }
  }

  /// 根据类型获取交易方总数
  Future<int> countByType(PartyType type) async {
    try {
      final db = await _databaseService.database;
      final finder = Finder(
        filter: Filter.equals('type', type.toString()),
      );
      final records = await _store.find(db, finder: finder);
      return records.length;
    } catch (e) {
      throw Exception('Failed to count parties by type: $e');
    }
  }
}
