import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/receipt_data.dart';
import '../models/voice_transaction_data.dart';
import '../utils/env_checker.dart';

class GeminiService {
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite-preview-06-17:generateContent';
  
  Future<ReceiptData?> analyzeReceipt(File imageFile) async {
    try {
      // Check environment configuration
      if (!EnvChecker.isConfigured()) {
        throw Exception(EnvChecker.getConfigurationStatus());
      }

      final apiKey = EnvChecker.getApiKey();
      if (apiKey.isEmpty) {
        throw Exception('Gemini API key not found. Please check your .env file and ensure it is properly loaded.');
      }

      // Read image file as bytes
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);

      // Determine MIME type based on file extension
      String mimeType = 'image/jpeg';
      final fileName = imageFile.path.toLowerCase();
      if (fileName.endsWith('.png')) {
        mimeType = 'image/png';
      } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
        mimeType = 'image/jpeg';
      } else if (fileName.endsWith('.webp')) {
        mimeType = 'image/webp';
      }

      // Prepare the request body
      final requestBody = {
        "contents": [
          {
            "parts": [
              {
                "text": """
Analyze this receipt/invoice image and extract the following information in JSON format:
{
  "merchant_name": "Name of the store/merchant",
  "date": "Date of purchase (YYYY-MM-DD format)",
  "address": "Store address",
  "items": [
    {
      "name": "Item name",
      "price": 0.00,
      "quantity": 1,
      "total": 0.00
    }
  ],
  "subtotal": 0.00,
  "tax": 0.00,
  "total": 0.00,
  "currency": "USD"
}

Please extract all visible items with their prices and quantities. If quantity is not specified, assume 1. Calculate totals accurately. Return only the JSON object, no additional text.
"""
              },
              {
                "inline_data": {
                  "mime_type": mimeType,
                  "data": base64Image
                }
              }
            ]
          }
        ],
        "generationConfig": {
            "thinkingConfig": {
              "thinkingBudget": 0
            },
          "temperature": 0.1,
          "topK": 32,
          "topP": 1,
          "maxOutputTokens": 4096,
        },
        "systemInstruction": {
          "parts": [
            {
              "text": "You are a receipt/invoice data extraction assistant. Extract information accurately and return only valid JSON."
            }
          ]
        }
      };

      // Make the API request
      final response = await http.post(
        Uri.parse('$_baseUrl?key=$apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final content = responseData['candidates']?[0]?['content']?['parts']?[0]?['text'];
        
        if (content != null) {
          // Clean the response to extract JSON
          String cleanedContent = content.toString().trim();
          
          // Remove markdown code blocks if present
          if (cleanedContent.startsWith('```json')) {
            cleanedContent = cleanedContent.substring(7);
          }
          if (cleanedContent.startsWith('```')) {
            cleanedContent = cleanedContent.substring(3);
          }
          if (cleanedContent.endsWith('```')) {
            cleanedContent = cleanedContent.substring(0, cleanedContent.length - 3);
          }
          
          cleanedContent = cleanedContent.trim();

          // Parse JSON with error handling
          try {
            final jsonData = jsonDecode(cleanedContent);
            return ReceiptData.fromJson(jsonData);
          } catch (jsonError) {
            print('JSON parsing error: $jsonError');
            print('Raw content: $cleanedContent');
            throw Exception('Failed to parse AI response as JSON: $jsonError');
          }
        }
      } else {
        throw Exception('Failed to analyze receipt: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Error analyzing receipt: $e');
      rethrow;
    }

    return null;
  }

  /// 分析语音转文本内容并提取交易信息
  Future<VoiceTransactionData?> analyzeVoiceText(String voiceText) async {
    try {
      // Check environment configuration
      if (!EnvChecker.isConfigured()) {
        throw Exception(EnvChecker.getConfigurationStatus());
      }

      final apiKey = EnvChecker.getApiKey();
      if (apiKey.isEmpty) {
        throw Exception('Gemini API key not found. Please check your .env file and ensure it is properly loaded.');
      }

      // Prepare the request body
      final requestBody = {
        "contents": [
          {
            "parts": [
              {
                "text": """
分析以下语音转文本内容，提取交易信息并以JSON格式返回：

语音内容：$voiceText

请提取以下信息：
{
  "merchant_name": "商家或交易对象名称",
  "amount": 0.00,
  "transaction_time": "交易时间(ISO 8601格式，如果未提及则使用当前时间)",
  "description": "交易描述或备注",
  "category": "交易类别（如餐饮、购物、交通等）",
  "payment_method": "支付方式（如现金、信用卡、支付宝等）",
  "location": "交易地点"
}

注意事项：
1. 如果语音中没有明确提到某个字段，请设置为null
2. 金额必须是数字格式
3. 时间格式使用ISO 8601标准
4. 如果没有提到时间，使用当前时间
5. 尽量从语音内容中推断合理的交易类别
6. 只返回JSON对象，不要包含其他文本

请仔细分析语音内容，准确提取交易信息。
"""
              }
            ]
          }
        ],
        "generationConfig": {
          "thinkingConfig": {
            "thinkingBudget": 0
          },
          "temperature": 0.1,
          "topK": 32,
          "topP": 1,
          "maxOutputTokens": 4096,
        },
        "systemInstruction": {
          "parts": [
            {
              "text": "你是一个专业的语音交易信息提取助手。请准确分析语音内容并提取交易相关信息，返回标准的JSON格式。"
            }
          ]
        }
      };

      // Make the API request
      final response = await http.post(
        Uri.parse('$_baseUrl?key=$apiKey'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final content = responseData['candidates']?[0]?['content']?['parts']?[0]?['text'];

        if (content != null) {
          // Clean the response to extract JSON
          String cleanedContent = content.toString().trim();

          // Remove markdown code blocks if present
          if (cleanedContent.startsWith('```json')) {
            cleanedContent = cleanedContent.substring(7);
          }
          if (cleanedContent.startsWith('```')) {
            cleanedContent = cleanedContent.substring(3);
          }
          if (cleanedContent.endsWith('```')) {
            cleanedContent = cleanedContent.substring(0, cleanedContent.length - 3);
          }

          cleanedContent = cleanedContent.trim();

          // Parse JSON with error handling
          try {
            final jsonData = jsonDecode(cleanedContent);

            // 如果没有提供交易时间，使用当前时间
            if (jsonData['transaction_time'] == null) {
              jsonData['transaction_time'] = DateTime.now().toIso8601String();
            }

            return VoiceTransactionData.fromJson(jsonData);
          } catch (jsonError) {
            print('JSON parsing error: $jsonError');
            print('Raw content: $cleanedContent');
            throw Exception('Failed to parse AI response as JSON: $jsonError');
          }
        }
      } else {
        throw Exception('Failed to analyze voice text: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Error analyzing voice text: $e');
      rethrow;
    }

    return null;
  }
}
